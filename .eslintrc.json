{
	"extends": [
		"next/core-web-vitals",
		"@typescript-eslint/recommended",
		"@typescript-eslint/recommended-requiring-type-checking",
		"plugin:import/recommended",
		"plugin:import/typescript",
		"plugin:jsx-a11y/recommended"
	],
	"parser": "@typescript-eslint/parser",
	"parserOptions": {
		"ecmaVersion": "latest",
		"sourceType": "module",
		"project": "./tsconfig.json"
	},
	"plugins": ["@typescript-eslint", "import", "jsx-a11y", "unused-imports"],
	"settings": {
		"import/resolver": {
			"typescript": {
				"alwaysTryTypes": true,
				"project": "./tsconfig.json"
			}
		}
	},
	"rules": {
		// TypeScript-specific rules for type safety
		"@typescript-eslint/no-explicit-any": "error",
		"@typescript-eslint/prefer-nullish-coalescing": "error",
		"@typescript-eslint/prefer-optional-chain": "error",
		"@typescript-eslint/no-non-null-assertion": "error",
		"@typescript-eslint/no-unnecessary-type-assertion": "error",
		"@typescript-eslint/no-unused-vars": [
			"error",
			{
				"argsIgnorePattern": "^_",
				"varsIgnorePattern": "^_"
			}
		],
		"@typescript-eslint/explicit-function-return-type": [
			"error",
			{
				"allowExpressions": true,
				"allowTypedFunctionExpressions": true,
				"allowHigherOrderFunctions": true,
				"allowDirectConstAssertionInArrowFunctions": true
			}
		],
		"@typescript-eslint/consistent-type-imports": [
			"error",
			{
				"prefer": "type-imports"
			}
		],
		"@typescript-eslint/consistent-type-definitions": ["error", "interface"],
		"@typescript-eslint/array-type": ["error", { "default": "array" }],
		"@typescript-eslint/prefer-readonly": "error",
		"@typescript-eslint/prefer-readonly-parameter-types": "off",
		"@typescript-eslint/switch-exhaustiveness-check": "error",

		// General code quality rules
		"prefer-const": "error",
		"no-var": "error",
		"no-console": ["warn", { "allow": ["warn", "error"] }],
		"eqeqeq": ["error", "always"],
		"curly": ["error", "all"],

		// Import/Export rules
		"import/order": [
			"error",
			{
				"groups": ["builtin", "external", "internal", "parent", "sibling", "index"],
				"newlines-between": "always",
				"alphabetize": {
					"order": "asc",
					"caseInsensitive": true
				}
			}
		],
		"import/no-unresolved": "error",
		"import/no-cycle": "error",
		"import/no-unused-modules": "error",
		"import/no-duplicates": "error",

		// Unused imports
		"unused-imports/no-unused-imports": "error",
		"unused-imports/no-unused-vars": [
			"error",
			{
				"vars": "all",
				"varsIgnorePattern": "^_",
				"args": "after-used",
				"argsIgnorePattern": "^_"
			}
		],

		// Accessibility rules
		"jsx-a11y/alt-text": "error",
		"jsx-a11y/anchor-has-content": "error",
		"jsx-a11y/anchor-is-valid": "error",
		"jsx-a11y/click-events-have-key-events": "error",
		"jsx-a11y/no-static-element-interactions": "error"
	},
	"overrides": [
		{
			"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
			"rules": {
				"@typescript-eslint/no-explicit-any": "off",
				"@typescript-eslint/no-unsafe-any": "off",
				"@typescript-eslint/no-unsafe-assignment": "off",
				"@typescript-eslint/no-unsafe-call": "off",
				"@typescript-eslint/no-unsafe-member-access": "off",
				"@typescript-eslint/no-unsafe-return": "off",
				"@typescript-eslint/explicit-function-return-type": "off",
				"import/no-unused-modules": "off"
			}
		},
		{
			"files": ["**/*.config.js", "**/*.config.ts", "**/*.config.cjs"],
			"rules": {
				"@typescript-eslint/no-var-requires": "off",
				"import/no-unused-modules": "off"
			}
		}
	]
}
